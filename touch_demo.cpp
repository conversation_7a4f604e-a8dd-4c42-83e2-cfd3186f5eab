#include <iostream>
#include <unistd.h>
#include <thread>
#include <chrono>
#include "kma/jni/driver.h"

class TouchDemo {
private:
    Driver driver;
    int screen_width;
    int screen_height;
    bool initialized;

public:
    TouchDemo(int width = 1080, int height = 2340) 
        : screen_width(width), screen_height(height), initialized(false) {
        std::cout << "TouchDemo 初始化中..." << std::endl;
        
        // 初始化触摸系统
        if (driver.uinput_init(screen_width, screen_height)) {
            initialized = true;
            std::cout << "触摸系统初始化成功! 分辨率: " << width << "x" << height << std::endl;
        } else {
            std::cout << "触摸系统初始化失败!" << std::endl;
        }
    }

    // 检查坐标是否有效
    bool isValidCoordinate(int x, int y) {
        return (x > 0 && x < screen_width && y > 0 && y < screen_height);
    }

    // 单击事件
    void click(int x, int y, int delay_ms = 100) {
        if (!initialized) {
            std::cout << "触摸系统未初始化!" << std::endl;
            return;
        }

        if (!isValidCoordinate(x, y)) {
            std::cout << "坐标无效: (" << x << ", " << y << ")" << std::endl;
            return;
        }

        std::cout << "执行单击: (" << x << ", " << y << ")" << std::endl;
        
        // 按下
        driver.uinput_down(x, y);
        
        // 延时
        std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
        
        // 抬起
        driver.uinput_up();
        
        std::cout << "单击完成" << std::endl;
    }

    // 随机单击
    void randomClick(int delay_ms = 100) {
        if (!initialized) {
            std::cout << "触摸系统未初始化!" << std::endl;
            return;
        }

        int x = driver.uinput_rand(screen_width);
        int y = driver.uinput_rand(screen_height);
        
        std::cout << "执行随机单击: (" << x << ", " << y << ")" << std::endl;
        click(x, y, delay_ms);
    }

    // 滑动事件
    void swipe(int start_x, int start_y, int end_x, int end_y, int steps = 10, int step_delay_ms = 50) {
        if (!initialized) {
            std::cout << "触摸系统未初始化!" << std::endl;
            return;
        }

        if (!isValidCoordinate(start_x, start_y) || !isValidCoordinate(end_x, end_y)) {
            std::cout << "滑动坐标无效!" << std::endl;
            return;
        }

        std::cout << "执行滑动: (" << start_x << ", " << start_y << ") -> (" 
                  << end_x << ", " << end_y << ")" << std::endl;

        // 按下起始点
        driver.uinput_down(start_x, start_y);
        std::this_thread::sleep_for(std::chrono::milliseconds(step_delay_ms));

        // 计算每步的移动距离
        float dx = (float)(end_x - start_x) / steps;
        float dy = (float)(end_y - start_y) / steps;

        // 执行滑动
        for (int i = 1; i <= steps; i++) {
            int current_x = start_x + (int)(dx * i);
            int current_y = start_y + (int)(dy * i);
            
            driver.uinput_move(current_x, current_y);
            std::this_thread::sleep_for(std::chrono::milliseconds(step_delay_ms));
        }

        // 抬起
        driver.uinput_up();
        std::cout << "滑动完成" << std::endl;
    }

    // 长按事件
    void longPress(int x, int y, int duration_ms = 1000) {
        if (!initialized) {
            std::cout << "触摸系统未初始化!" << std::endl;
            return;
        }

        if (!isValidCoordinate(x, y)) {
            std::cout << "坐标无效: (" << x << ", " << y << ")" << std::endl;
            return;
        }

        std::cout << "执行长按: (" << x << ", " << y << ") 持续 " << duration_ms << "ms" << std::endl;
        
        // 按下
        driver.uinput_down(x, y);
        
        // 长时间延时
        std::this_thread::sleep_for(std::chrono::milliseconds(duration_ms));
        
        // 抬起
        driver.uinput_up();
        
        std::cout << "长按完成" << std::endl;
    }

    // 多点触摸模拟（连续快速点击）
    void multiTap(int x, int y, int tap_count = 3, int tap_interval_ms = 200) {
        std::cout << "执行多次点击: (" << x << ", " << y << ") " << tap_count << "次" << std::endl;
        
        for (int i = 0; i < tap_count; i++) {
            click(x, y, 50);
            if (i < tap_count - 1) {
                std::this_thread::sleep_for(std::chrono::milliseconds(tap_interval_ms));
            }
        }
        
        std::cout << "多次点击完成" << std::endl;
    }

    // 画圆形轨迹
    void drawCircle(int center_x, int center_y, int radius, int steps = 20) {
        if (!initialized) {
            std::cout << "触摸系统未初始化!" << std::endl;
            return;
        }

        std::cout << "绘制圆形轨迹: 中心(" << center_x << ", " << center_y 
                  << ") 半径" << radius << std::endl;

        // 计算圆形轨迹上的点
        const double PI = 3.14159265359;
        double angle_step = 2 * PI / steps;

        // 起始点
        int start_x = center_x + radius;
        int start_y = center_y;
        
        driver.uinput_down(start_x, start_y);
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // 绘制圆形
        for (int i = 1; i <= steps; i++) {
            double angle = angle_step * i;
            int x = center_x + (int)(radius * cos(angle));
            int y = center_y + (int)(radius * sin(angle));
            
            if (isValidCoordinate(x, y)) {
                driver.uinput_move(x, y);
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        }

        driver.uinput_up();
        std::cout << "圆形轨迹绘制完成" << std::endl;
    }

    // 获取屏幕信息
    void getScreenInfo() {
        std::cout << "屏幕分辨率: " << screen_width << "x" << screen_height << std::endl;
        std::cout << "触摸系统状态: " << (initialized ? "已初始化" : "未初始化") << std::endl;
    }
};

// 演示函数
void runDemo() {
    std::cout << "=== 触摸模拟演示程序 ===" << std::endl;
    
    // 创建TouchDemo实例（默认1080x2340分辨率）
    TouchDemo demo(1080, 2340);
    
    demo.getScreenInfo();
    std::cout << std::endl;

    // 演示1: 单击屏幕中心
    std::cout << "演示1: 单击屏幕中心" << std::endl;
    demo.click(540, 1170);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    // 演示2: 随机点击
    std::cout << "\n演示2: 随机点击" << std::endl;
    demo.randomClick();
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    // 演示3: 向上滑动（模拟上滑手势）
    std::cout << "\n演示3: 向上滑动" << std::endl;
    demo.swipe(540, 1800, 540, 600, 15, 30);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    // 演示4: 长按
    std::cout << "\n演示4: 长按" << std::endl;
    demo.longPress(540, 1170, 1500);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    // 演示5: 多次点击
    std::cout << "\n演示5: 多次点击" << std::endl;
    demo.multiTap(540, 1170, 3, 300);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    // 演示6: 绘制圆形
    std::cout << "\n演示6: 绘制圆形轨迹" << std::endl;
    demo.drawCircle(540, 1170, 200, 24);

    std::cout << "\n=== 演示完成 ===" << std::endl;
}

int main() {
    try {
        runDemo();
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
