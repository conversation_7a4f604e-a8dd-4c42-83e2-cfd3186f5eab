# 触摸模拟演示程序

基于 `kma/jni/driver.h` 驱动接口的触摸模拟演示程序。

## 文件说明

- `simple_touch_demo.cpp` - 简单版触摸演示，包含基本的触摸操作
- `touch_demo.cpp` - 完整版触摸演示，包含高级触摸功能和封装类
- `Makefile` - 编译脚本
- `README_touch_demo.md` - 说明文档

## 编译方法

```bash
# 编译所有演示程序
make all

# 或者单独编译
make simple_touch_demo
make touch_demo
```

## 运行方法

```bash
# 运行简单版演示
make run-simple
# 或者直接运行
./simple_touch_demo

# 运行完整版演示
make run-full
# 或者直接运行
./touch_demo
```

## 功能演示

### 简单版演示 (simple_touch_demo.cpp)
1. **单击** - 点击屏幕中心
2. **向上滑动** - 从屏幕下方滑动到上方
3. **长按** - 长按屏幕中心1.5秒
4. **随机点击** - 在随机位置点击
5. **连续点击** - 快速连续点击3次
6. **水平滑动** - 从左到右滑动

### 完整版演示 (touch_demo.cpp)
包含 TouchDemo 类，提供以下功能：
- `click(x, y)` - 单击指定坐标
- `randomClick()` - 随机位置点击
- `swipe(x1, y1, x2, y2)` - 滑动手势
- `longPress(x, y, duration)` - 长按
- `multiTap(x, y, count)` - 多次点击
- `drawCircle(cx, cy, radius)` - 绘制圆形轨迹

## 驱动接口说明

### 初始化
```cpp
Driver driver;
driver.uinput_init(width, height);  // 初始化触摸系统，传入屏幕分辨率
```

### 基本触摸操作
```cpp
// 单击
driver.uinput_down(x, y);    // 按下
usleep(100000);              // 延时100ms
driver.uinput_up();          // 抬起

// 滑动
driver.uinput_down(x1, y1);  // 起始点按下
driver.uinput_move(x2, y2);  // 移动到中间点
driver.uinput_move(x3, y3);  // 移动到结束点
driver.uinput_up();          // 抬起
```

### 重要注意事项

1. **必须初始化**: 使用触摸功能前必须调用 `uinput_init(width, height)`
2. **必须抬起**: 每次触摸操作完成后都必须调用 `uinput_up()`，否则会出现卡屏
3. **坐标范围**: 
   - 竖屏: 0 < x < width, 0 < y < height
   - 横屏: 0 < y < width, 0 < x < height
4. **线程安全**: 一个对象不能用于多线程，但一个线程可以使用多个对象
5. **延时控制**: 适当的延时可以让触摸操作更自然

### 坐标系统
- 以屏幕左上角为原点 (0,0)
- X轴向右递增
- Y轴向下递增
- 常见分辨率: 1080x2340 (现代Android手机)

### 随机功能
```cpp
int rand_x = driver.uinput_rand(width);   // 获取有效的随机X坐标
int rand_y = driver.uinput_rand(height);  // 获取有效的随机Y坐标
```

## 应用场景

- 自动化测试
- 游戏脚本
- UI自动化
- 无障碍辅助
- 设备控制

## 错误处理

如果出现以下问题：
- **卡屏/无反应**: 检查是否忘记调用 `uinput_up()`
- **坐标无效**: 检查坐标是否在有效范围内
- **初始化失败**: 检查权限和驱动是否正确加载

## 系统要求

- 支持内核版本: 4.9 ~ 6.6
- 需要root权限
- 支持QGKI、GKI2.0+
- Android设备
