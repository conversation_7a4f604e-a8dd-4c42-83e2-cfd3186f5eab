# Makefile for Touch Demo
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
INCLUDES = -I.
LIBS = -lpthread

# 目标文件
TARGETS = touch_demo simple_touch_demo

# 默认目标
all: $(TARGETS)

# 编译完整版触摸演示
touch_demo: touch_demo.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIBS)

# 编译简单版触摸演示
simple_touch_demo: simple_touch_demo.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIBS)

# 清理编译文件
clean:
	rm -f $(TARGETS)

# 运行简单演示
run-simple: simple_touch_demo
	./simple_touch_demo

# 运行完整演示
run-full: touch_demo
	./touch_demo

# 显示帮助信息
help:
	@echo "可用的目标:"
	@echo "  all          - 编译所有演示程序"
	@echo "  touch_demo   - 编译完整版触摸演示"
	@echo "  simple_touch_demo - 编译简单版触摸演示"
	@echo "  run-simple   - 运行简单版演示"
	@echo "  run-full     - 运行完整版演示"
	@echo "  clean        - 清理编译文件"
	@echo "  help         - 显示此帮助信息"

.PHONY: all clean run-simple run-full help
