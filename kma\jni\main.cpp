#include <iostream>
#include <unistd.h>
#include <thread>
#include <chrono>
#include "driver.h"

int main() {
    std::cout << "=== 简单触摸模拟演示 ===" << std::endl;
    
    // 创建驱动实例
    Driver driver;
    
    // 设置屏幕分辨率 (1080x2340 - 常见的Android手机分辨率)
    int width = 1080;
    int height = 2340;
    
    std::cout << "初始化触摸系统，分辨率: " << width << "x" << height << std::endl;
    
    // 初始化触摸系统
    if (!driver.uinput_init(width, height)) {
        std::cerr << "触摸系统初始化失败!" << std::endl;
        return 1;
    }
    
    std::cout << "触摸系统初始化成功!" << std::endl;
    std::cout << std::endl;
    
    // 等待一秒
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    // 演示1: 单击屏幕中心
    std::cout << "演示1: 单击屏幕中心 (540, 1170)" << std::endl;
    driver.uinput_down(540, 1170);  // 按下
    std::this_thread::sleep_for(std::chrono::milliseconds(100));  // 延时100ms
    driver.uinput_up();  // 抬起
    std::cout << "单击完成" << std::endl;
    std::cout << std::endl;
    
    // 等待2秒
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 演示2: 向上滑动手势
    std::cout << "演示2: 向上滑动 (540, 1800) -> (540, 600)" << std::endl;
    driver.uinput_down(540, 1800);  // 在屏幕下方按下
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    // 分10步滑动到上方
    for (int i = 1; i <= 10; i++) {
        int y = 1800 - (1200 * i / 10);  // 从1800滑动到600
        driver.uinput_move(540, y);
        std::this_thread::sleep_for(std::chrono::milliseconds(30));
    }
    
    driver.uinput_up();  // 抬起
    std::cout << "滑动完成" << std::endl;
    std::cout << std::endl;
    
    // 等待2秒
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 演示3: 长按
    std::cout << "演示3: 长按屏幕中心 1.5秒" << std::endl;
    driver.uinput_down(540, 1170);  // 按下
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));  // 长按1.5秒
    driver.uinput_up();  // 抬起
    std::cout << "长按完成" << std::endl;
    std::cout << std::endl;
    
    // 等待2秒
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 演示4: 随机点击
    std::cout << "演示4: 随机点击" << std::endl;
    int rand_x = driver.uinput_rand(width);
    int rand_y = driver.uinput_rand(height);
    std::cout << "随机坐标: (" << rand_x << ", " << rand_y << ")" << std::endl;
    
    driver.uinput_down(rand_x, rand_y);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    driver.uinput_up();
    std::cout << "随机点击完成" << std::endl;
    std::cout << std::endl;
    
    // 等待2秒
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 演示5: 连续快速点击
    std::cout << "演示5: 连续快速点击3次" << std::endl;
    for (int i = 0; i < 3; i++) {
        std::cout << "第 " << (i+1) << " 次点击" << std::endl;
        driver.uinput_down(540, 1170);
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        driver.uinput_up();
        std::this_thread::sleep_for(std::chrono::milliseconds(200));  // 点击间隔
    }
    std::cout << "连续点击完成" << std::endl;
    std::cout << std::endl;
    
    // 等待2秒
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    
    // 演示6: 水平滑动
    std::cout << "演示6: 水平滑动 (200, 1170) -> (880, 1170)" << std::endl;
    driver.uinput_down(200, 1170);  // 在屏幕左侧按下
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    // 分8步滑动到右侧
    for (int i = 1; i <= 8; i++) {
        int x = 200 + (680 * i / 8);  // 从200滑动到880
        driver.uinput_move(x, 1170);
        std::this_thread::sleep_for(std::chrono::milliseconds(40));
    }
    
    driver.uinput_up();  // 抬起
    std::cout << "水平滑动完成" << std::endl;
    std::cout << std::endl;
    
    std::cout << "=== 所有演示完成 ===" << std::endl;
    std::cout << "注意事项:" << std::endl;
    std::cout << "1. 每次触摸操作后都必须调用 uinput_up() 抬起" << std::endl;
    std::cout << "2. 坐标范围: x(0-" << width << "), y(0-" << height << ")" << std::endl;
    std::cout << "3. 滑动时使用 uinput_move() 在按下和抬起之间" << std::endl;
    std::cout << "4. 适当的延时可以让触摸更自然" << std::endl;
    
    return 0;
}
